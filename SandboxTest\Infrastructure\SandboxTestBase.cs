using System.Text.Json;
using System.Text.Json.Serialization;
using Xunit;
using Xunit.Abstractions;
using FluentAssertions;
using System.Net;
using System.Text;

namespace SandboxTest.Infrastructure
{
    /// <summary>
    /// Base class for all Sandbox API tests providing common functionality
    /// </summary>
    public abstract class SandboxTestBase : IAsyncLifetime
    {
        protected readonly SandboxApiFactory Factory;
        protected readonly HttpClient Client;
        protected readonly JsonSerializerOptions JsonOptions;
        protected readonly ITestOutputHelper Output;

        // Test marker GUID to check if sandbox data is already loaded
        protected static readonly Guid TestMarkerGuid = new Guid("*************-9999-9999-************");

        // Static flag to ensure one-time initialization
        private static bool _sandboxInitialized = false;
        private static readonly object _initLock = new object();

        protected SandboxTestBase(ITestOutputHelper output)
        {
            Output = output;
            Factory = new SandboxApiFactory();
            Client = Factory.CreateClient();
            Client.DefaultRequestHeaders.Add("sandboxId", Factory.SandboxId);

            JsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            };
            JsonOptions.Converters.Add(new JsonStringEnumConverter());
        }

        /// <summary>
        /// Initialize sandbox data before tests run (only once)
        /// </summary>
        public virtual async Task InitializeAsync()
        {
            // Use lock to ensure thread-safe one-time initialization
            lock (_initLock)
            {
                if (_sandboxInitialized)
                {
                    Output.WriteLine("✅ Sandbox data already initialized, skipping...");
                    return;
                }
            }

            try
            {
                Output.WriteLine("🔍 Checking if sandbox data is already loaded...");

                // Check if test marker data exists in sandbox
                var isDataLoaded = await CheckIfSandboxDataExists();

                if (isDataLoaded)
                {
                    Output.WriteLine("✅ Sandbox data already exists, skipping initialization");
                    lock (_initLock)
                    {
                        _sandboxInitialized = true;
                    }
                    return;
                }

                Output.WriteLine("🔄 Loading sandbox data for the first time...");

                // Initialize the sandbox first
                await Factory.InitializeSandboxAsync();

                // Load test data from JSON files in Assets folder
                await LoadSandboxTestData();

                // Create test marker to indicate data is loaded
                await CreateTestMarker();

                lock (_initLock)
                {
                    _sandboxInitialized = true;
                }

                Output.WriteLine("✅ Sandbox initialization completed successfully");
            }
            catch (Exception ex)
            {
                Output.WriteLine($"❌ Sandbox initialization failed: {ex.Message}");
                throw;
            }
        }

        public virtual Task DisposeAsync()
        {
            Client.Dispose();
            Factory.Dispose();
            return Task.CompletedTask;
        }

        /// <summary>
        /// Helper method to deserialize HTTP response content to specified type
        /// </summary>
        protected async Task<T?> DeserializeResponseAsync<T>(HttpResponseMessage response)
        {
            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<T>(content, JsonOptions);
        }

        /// <summary>
        /// Helper method to serialize object to JSON content
        /// </summary>
        protected StringContent SerializeToJsonContent<T>(T obj)
        {
            var json = JsonSerializer.Serialize(obj, JsonOptions);
            return new StringContent(json, Encoding.UTF8, "application/json");
        }

        /// <summary>
        /// Check if sandbox data is already loaded by looking for test marker
        /// </summary>
        private async Task<bool> CheckIfSandboxDataExists()
        {
            try
            {
                // Try to get a wallet with our test marker GUID
                var response = await Client.GetAsync($"/wallet/{TestMarkerGuid}");

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    Output.WriteLine($"✅ Test marker found: {TestMarkerGuid}");
                    return true;
                }
                else if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    Output.WriteLine($"❌ Test marker not found: {TestMarkerGuid}");
                    return false;
                }
                else
                {
                    Output.WriteLine($"⚠️ Unexpected response when checking test marker: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Output.WriteLine($"⚠️ Error checking sandbox data existence: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Load test data from JSON files in Assets folder
        /// </summary>
        private async Task LoadSandboxTestData()
        {
            try
            {
                var assetsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets");

                if (!Directory.Exists(assetsPath))
                {
                    Output.WriteLine($"⚠️ Assets directory not found: {assetsPath}");
                    return;
                }

                var testDataFiles = Directory.GetFiles(assetsPath, "*.json");

                if (testDataFiles.Length == 0)
                {
                    Output.WriteLine("⚠️ No JSON test data files found in Assets folder");
                    return;
                }

                foreach (var file in testDataFiles)
                {
                    Output.WriteLine($"📁 Loading test data from: {Path.GetFileName(file)}");
                    var jsonContent = await File.ReadAllTextAsync(file);

                    // Parse and validate JSON
                    var testData = JsonSerializer.Deserialize<object>(jsonContent, JsonOptions);
                    Output.WriteLine($"✅ Successfully loaded {Path.GetFileName(file)}");

                    // Here you could add specific logic to import the data into the sandbox
                    // For example, if it's wallet data, user data, etc.
                }
            }
            catch (Exception ex)
            {
                Output.WriteLine($"❌ Error loading test data: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Create a test marker to indicate sandbox data has been loaded
        /// </summary>
        private async Task CreateTestMarker()
        {
            try
            {
                // Create a test wallet with our marker GUID to indicate data is loaded
                var testMarkerWallet = new
                {
                    walletId = TestMarkerGuid,
                    walletName = "TEST_MARKER_WALLET_DO_NOT_DELETE",
                    description = "This wallet serves as a marker to indicate sandbox test data has been loaded",
                    isTestMarker = true
                };

                var content = SerializeToJsonContent(testMarkerWallet);
                var response = await Client.PostAsync("/wallet/register", content);

                if (response.IsSuccessStatusCode)
                {
                    Output.WriteLine($"✅ Test marker created successfully: {TestMarkerGuid}");
                }
                else
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Output.WriteLine($"⚠️ Failed to create test marker: {response.StatusCode} - {responseContent}");
                    // Don't throw here as the marker creation might fail due to API limitations
                    // but the test data loading might still be successful
                }
            }
            catch (Exception ex)
            {
                Output.WriteLine($"⚠️ Error creating test marker: {ex.Message}");
                // Don't throw here as the marker creation is not critical for test functionality
            }
        }

        /// <summary>
        /// Helper method to assert successful response and deserialize content
        /// </summary>
        protected async Task<T> AssertSuccessAndDeserialize<T>(HttpResponseMessage response)
        {
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var result = await DeserializeResponseAsync<T>(response);
            result.Should().NotBeNull();
            return result!;
        }

        /// <summary>
        /// Helper method to assert error response with specific status code
        /// </summary>
        protected async Task AssertErrorResponse(HttpResponseMessage response, HttpStatusCode expectedStatusCode)
        {
            response.StatusCode.Should().Be(expectedStatusCode);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().NotBeNullOrEmpty();
        }
    }
}
