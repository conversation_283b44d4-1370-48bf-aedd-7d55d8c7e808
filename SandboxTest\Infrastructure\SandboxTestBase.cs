using System.Text.Json;
using System.Text.Json.Serialization;
using Xunit;
using FluentAssertions;
using System.Net;
using System.Text;

namespace SandboxTest.Infrastructure
{
    /// <summary>
    /// Base class for all Sandbox API tests providing common functionality
    /// </summary>
    public abstract class SandboxTestBase : IAsyncLifetime
    {
        protected readonly SandboxApiFactory Factory;
        protected readonly HttpClient Client;
        protected readonly JsonSerializerOptions JsonOptions;

        protected SandboxTestBase()
        {
            Factory = new SandboxApiFactory();
            Client = Factory.CreateClient();
            Client.DefaultRequestHeaders.Add("sandboxId", Factory.SandboxId);

            JsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            };
            JsonOptions.Converters.Add(new JsonStringEnumConverter());
        }

        public virtual async Task InitializeAsync()
        {
            await Factory.InitializeSandboxAsync();
        }

        public virtual Task DisposeAsync()
        {
            Client.Dispose();
            Factory.Dispose();
            return Task.CompletedTask;
        }

        /// <summary>
        /// Helper method to deserialize HTTP response content to specified type
        /// </summary>
        protected async Task<T?> DeserializeResponseAsync<T>(HttpResponseMessage response)
        {
            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<T>(content, JsonOptions);
        }

        /// <summary>
        /// Helper method to serialize object to JSON content
        /// </summary>
        protected StringContent SerializeToJsonContent<T>(T obj)
        {
            var json = JsonSerializer.Serialize(obj, JsonOptions);
            return new StringContent(json, Encoding.UTF8, "application/json");
        }

        /// <summary>
        /// Helper method to assert successful response and deserialize content
        /// </summary>
        protected async Task<T> AssertSuccessAndDeserialize<T>(HttpResponseMessage response)
        {
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var result = await DeserializeResponseAsync<T>(response);
            result.Should().NotBeNull();
            return result!;
        }

        /// <summary>
        /// Helper method to assert error response with specific status code
        /// </summary>
        protected async Task AssertErrorResponse(HttpResponseMessage response, HttpStatusCode expectedStatusCode)
        {
            response.StatusCode.Should().Be(expectedStatusCode);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().NotBeNullOrEmpty();
        }
    }
}
