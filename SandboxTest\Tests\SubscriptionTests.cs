using FluentAssertions;
using SandboxTest.Infrastructure;
using SandboxTest.Models;
using System.Net;
using Xunit;

namespace SandboxTest.Tests
{
    /// <summary>
    /// Comprehensive tests for Subscription functionality in the Sandbox API
    /// 
    /// Test Logic Overview:
    /// This test class covers all subscription-related operations including:
    /// 1. Subscription Creation - Creating new subscriptions with different tiers
    /// 2. Subscription Retrieval - Getting subscription details and status
    /// 3. Subscription Management - Updating and managing subscription lifecycle
    /// 4. Subscription Usage - Tracking subscription usage and limits
    /// 5. Subscription Opt-out - Canceling subscriptions
    /// 
    /// Each test validates the subscription state, billing information,
    /// and ensures proper business logic enforcement.
    /// </summary>
    public class SubscriptionTests : SandboxTestBase
    {
        [Fact(DisplayName = "Create Subscription - Should create subscription with specified tier")]
        public async Task CreateSubscription_ShouldCreateSubscription_WithSpecifiedTier()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet to associate the subscription with
             * 2. Create a subscription with a specific tier (Premium)
             * 3. Verify the subscription is created successfully
             * 4. Verify subscription details match the request
             * 5. Verify subscription can be retrieved
             * 
             * Expected Result:
             * - Subscription is created with correct tier
             * - Subscription has valid ID and dates
             * - Subscription amount matches tier pricing
             * - Subscription status is properly set
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            var subscriptionRequest = TestDataHelper.CreateSubscriptionRequest("Premium");

            // Act
            var subscriptionResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/subscription", subscriptionRequest);

            // Assert
            var subscription = await AssertSuccessAndDeserialize<SubscriptionResponse>(subscriptionResponse);
            
            subscription.SubscriptionId.Should().NotBeEmpty("subscription should have a valid ID");
            subscription.Tier.Should().Be("Premium", "subscription tier should match request");
            subscription.Amount.Should().BeGreaterThan(0, "subscription should have a positive amount");
            subscription.StartDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(5), "start date should be recent");
            subscription.EndDate.Should().BeAfter(subscription.StartDate, "end date should be after start date");
            subscription.Status.Should().NotBeNullOrEmpty("subscription should have a status");
            subscription.SubscriptionBundles.Should().NotBeEmpty("subscription should have bundles");

            // Verify subscription can be retrieved
            var getSubscriptionResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/subscription");
            var retrievedSubscription = await AssertSuccessAndDeserialize<SubscriptionResponse>(getSubscriptionResponse);
            
            retrievedSubscription.SubscriptionId.Should().Be(subscription.SubscriptionId, "retrieved subscription should match created subscription");
            retrievedSubscription.Tier.Should().Be("Premium", "retrieved subscription tier should match");
        }

        [Fact(DisplayName = "Create Subscription - Should create Basic tier subscription with different pricing")]
        public async Task CreateSubscription_ShouldCreateBasicTierSubscription_WithDifferentPricing()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet for subscription
             * 2. Create a Basic tier subscription
             * 3. Verify Basic tier has different pricing than Premium
             * 4. Verify Basic tier has appropriate bundle configuration
             * 
             * Expected Result:
             * - Basic subscription is created successfully
             * - Basic tier pricing differs from Premium
             * - Bundle configuration matches Basic tier features
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            var subscriptionRequest = TestDataHelper.CreateSubscriptionRequest("Basic");

            // Act
            var subscriptionResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/subscription", subscriptionRequest);

            // Assert
            var subscription = await AssertSuccessAndDeserialize<SubscriptionResponse>(subscriptionResponse);
            
            subscription.Tier.Should().Be("Basic", "subscription tier should be Basic");
            subscription.Amount.Should().BeGreaterThan(0, "Basic subscription should have a positive amount");
            subscription.SubscriptionBundles.Should().NotBeEmpty("Basic subscription should have bundles");
            
            // Basic tier should typically have lower limits than Premium
            var transferBundle = subscription.SubscriptionBundles.FirstOrDefault(b => b.TransactionType == "Transfer");
            transferBundle.Should().NotBeNull("Basic subscription should include transfer bundle");
        }

        [Fact(DisplayName = "Get Subscription - Should return subscription details for wallet")]
        public async Task GetSubscription_ShouldReturnSubscriptionDetails_ForWallet()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and subscription
             * 2. Retrieve the subscription details
             * 3. Verify all subscription information is returned
             * 4. Verify subscription status and billing information
             * 
             * Expected Result:
             * - Subscription details are complete and accurate
             * - Billing information is properly calculated
             * - Status reflects current subscription state
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            var subscriptionRequest = TestDataHelper.CreateSubscriptionRequest("Premium");
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/subscription", subscriptionRequest);

            // Act
            var getSubscriptionResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/subscription");

            // Assert
            var subscription = await AssertSuccessAndDeserialize<SubscriptionResponse>(getSubscriptionResponse);
            
            subscription.SubscriptionId.Should().NotBeEmpty("subscription should have a valid ID");
            subscription.Tier.Should().Be("Premium", "subscription tier should be Premium");
            subscription.Amount.Should().BeGreaterThan(0, "subscription amount should be positive");
            subscription.DueAmount.Should().BeGreaterOrEqualTo(0, "due amount should be non-negative");
            subscription.StartDate.Should().BeBefore(subscription.EndDate, "start date should be before end date");
            subscription.PaymentDue.Should().BeAfter(DateTime.UtcNow.AddDays(-1), "payment due should be reasonable");
            subscription.Status.Should().BeOneOf("Active", "PendingPayment", "Trial", "Expired", "status should be valid");
            subscription.OptOut.Should().BeFalse("new subscription should not be opted out");
            
            foreach (var bundle in subscription.SubscriptionBundles)
            {
                bundle.TransactionType.Should().NotBeNullOrEmpty("bundle should have transaction type");
                bundle.Value.Should().BeGreaterThan(0, "bundle value should be positive");
            }
        }

        [Fact(DisplayName = "Get Subscription - Should return not found for wallet without subscription")]
        public async Task GetSubscription_ShouldReturnNotFound_ForWalletWithoutSubscription()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet without creating a subscription
             * 2. Attempt to retrieve subscription details
             * 3. Verify appropriate error response
             * 
             * Expected Result:
             * - API returns 404 Not Found
             * - Error indicates no subscription exists
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Act
            var getSubscriptionResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/subscription");

            // Assert
            await AssertErrorResponse(getSubscriptionResponse, HttpStatusCode.NotFound);
        }

        [Fact(DisplayName = "Get Subscription Usage - Should return usage information for active subscription")]
        public async Task GetSubscriptionUsage_ShouldReturnUsageInformation_ForActiveSubscription()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and subscription
             * 2. Perform some transactions to generate usage
             * 3. Retrieve subscription usage information
             * 4. Verify usage data is accurate and complete
             * 
             * Expected Result:
             * - Usage information is returned
             * - Usage reflects actual transaction activity
             * - Limits and remaining allowances are calculated correctly
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            var subscriptionRequest = TestDataHelper.CreateSubscriptionRequest("Premium");
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/subscription", subscriptionRequest);

            // Perform some wallet operations to generate usage
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(100);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Act
            var usageResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/subscription/usage");

            // Assert
            if (usageResponse.StatusCode == HttpStatusCode.OK)
            {
                var usage = await DeserializeResponseAsync<object>(usageResponse);
                usage.Should().NotBeNull("usage information should be returned");
                
                // Note: The exact structure of usage response would depend on the API implementation
                // This test validates that the endpoint is accessible and returns data
            }
            else
            {
                // Some implementations might not have usage tracking implemented yet
                usageResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotImplemented, HttpStatusCode.NotFound);
            }
        }

        [Fact(DisplayName = "Opt Out Subscription - Should cancel subscription successfully")]
        public async Task OptOutSubscription_ShouldCancelSubscription_Successfully()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and active subscription
             * 2. Opt out of the subscription
             * 3. Verify the opt-out operation succeeds
             * 4. Verify subscription status is updated to reflect cancellation
             * 
             * Expected Result:
             * - Opt-out operation succeeds
             * - Subscription is marked as opted out
             * - Subscription remains accessible but with cancelled status
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            var subscriptionRequest = TestDataHelper.CreateSubscriptionRequest("Premium");
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/subscription", subscriptionRequest);

            // Act
            var optOutResponse = await Client.PutAsync($"wallet/{createdWallet.WalletId}/subscription/opt-out", null);

            // Assert
            optOutResponse.StatusCode.Should().Be(HttpStatusCode.OK, "opt-out operation should succeed");

            // Verify subscription is marked as opted out
            var getSubscriptionResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/subscription");
            var subscription = await AssertSuccessAndDeserialize<SubscriptionResponse>(getSubscriptionResponse);
            
            subscription.OptOut.Should().BeTrue("subscription should be marked as opted out");
            subscription.Status.Should().BeOneOf("Cancelled", "OptedOut", "Inactive", "subscription status should reflect cancellation");
        }

        [Fact(DisplayName = "Update Subscription - Should upgrade subscription tier successfully")]
        public async Task UpdateSubscription_ShouldUpgradeSubscriptionTier_Successfully()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet with Basic subscription
             * 2. Upgrade to Premium subscription
             * 3. Verify the upgrade operation succeeds
             * 4. Verify subscription details reflect the new tier
             * 5. Verify pricing and features are updated
             * 
             * Expected Result:
             * - Subscription upgrade succeeds
             * - Tier is updated to Premium
             * - Pricing reflects Premium tier
             * - Bundle features are upgraded
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Create Basic subscription first
            var basicSubscriptionRequest = TestDataHelper.CreateSubscriptionRequest("Basic");
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/subscription", basicSubscriptionRequest);

            // Get initial subscription details
            var initialSubscriptionResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/subscription");
            var initialSubscription = await AssertSuccessAndDeserialize<SubscriptionResponse>(initialSubscriptionResponse);

            // Act - Upgrade to Premium
            var upgradeRequest = TestDataHelper.CreateSubscriptionRequest("Premium");
            var upgradeResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/subscription", upgradeRequest);

            // Assert
            var upgradedSubscription = await AssertSuccessAndDeserialize<SubscriptionResponse>(upgradeResponse);
            
            upgradedSubscription.Tier.Should().Be("Premium", "subscription should be upgraded to Premium");
            upgradedSubscription.Amount.Should().BeGreaterThan(initialSubscription.Amount, "Premium should cost more than Basic");
            upgradedSubscription.SubscriptionBundles.Should().NotBeEmpty("Premium subscription should have bundles");
            
            // Verify the upgrade is persisted
            var verifyResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/subscription");
            var verifiedSubscription = await AssertSuccessAndDeserialize<SubscriptionResponse>(verifyResponse);
            
            verifiedSubscription.Tier.Should().Be("Premium", "upgraded subscription should be persisted");
        }
    }
}
