<Project Sdk="Microsoft.NET.Sdk.Web">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <SignAssembly>true</SignAssembly>
        <AssemblyOriginatorKeyFile>Nbg.snk</AssemblyOriginatorKeyFile>
        <PreserveCompilationContext>true</PreserveCompilationContext>
        <Configurations>Debug;Development;Production;UAT;QAIDS;DeveloperPortal;Sandbox</Configurations>
        <EnforceCodeStyleInBuild>True</EnforceCodeStyleInBuild>
    </PropertyGroup>

    <ItemGroup>
        <_ContentIncludedByDefault Remove="appsettings.Development.json" />
        <_ContentIncludedByDefault Remove="appsettings.Production.json" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Controllers" Version="6.0.2" />
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Extensions" Version="6.0.2" />
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Implementation" Version="6.0.2" />
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Interfaces" Version="6.0.2" />
        <PackageReference Include="Nbg.NetCore.ApiSandbox.Types" Version="6.0.2" />
        <PackageReference Include="Nbg.NetCore.iBank.Profile.Implementation" Version="8.0.4" />
        <PackageReference Include="Nbg.NetCore.iBank.Profile.Interfaces" Version="8.0.0" />
        <PackageReference Include="Nbg.NetCore.Sca.Filter" Version="8.0.2" />
        <PackageReference Include="Nbg.OpenBanking.ConsentConnector" Version="4.0.1" />
        <PackageReference Include="NLog.Database" Version="5.3.4" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.15" />
        <PackageReference Include="NLog.WindowsEventLog" Version="5.3.4" />
        <PackageReference Include="NLog.WindowsIdentity" Version="5.3.0" />
        <PackageReference Include="Nbg.NetCore.Healthchecks" Version="8.0.3" />
        <!--<PackageReference Include="PDFsharp-MigraDoc-GDI" Version="6.1.1" />-->
        <PackageReference Include="Swashbuckle.AspNetCore" Version="7.0.0" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\..\Apis\Core\Nbg.Ewallet.Api.Controllers\Nbg.Ewallet.Api.Controllers.csproj" />
        <ProjectReference Include="..\..\..\Apis\Core\Nbg.Ewallet.Api.Interfaces\Nbg.Ewallet.Api.Interfaces.csproj" />
        <ProjectReference Include="..\..\..\Apis\Core\Nbg.Ewallet.Repository\Nbg.Ewallet.Repository.csproj" />
        <ProjectReference Include="..\..\..\Apis\Sandbox\Nbg.Ewallet.Api.Controllers\Nbg.Ewallet.Api.Sandbox.Controllers.csproj" />
        <ProjectReference Include="..\..\..\Apis\Sandbox\Nbg.Ewallet.Api.Sandbox.Implementation\Nbg.Ewallet.Api.Sandbox.Implementation.csproj" />
    </ItemGroup>
    <ItemGroup>
        <Folder Include="Properties\PublishProfiles\" />
    </ItemGroup>
    <ItemGroup>
        <Content Remove="Assets\**" />
    </ItemGroup>
    <ItemGroup>
        <None Include="Assets\**" />
    </ItemGroup>

</Project>