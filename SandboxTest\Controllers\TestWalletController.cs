using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace SandboxTest.Controllers
{
    /// <summary>
    /// Test controller that simulates wallet operations for testing purposes
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class TestWalletController : ControllerBase
    {
        /// <summary>
        /// Register a new wallet
        /// </summary>
        [HttpPost("register")]
        public IActionResult RegisterWallet([FromBody] WalletRegisterRequest request)
        {
            if (string.IsNullOrEmpty(request?.WalletName))
            {
                return BadRequest(new { message = "Wallet name is required" });
            }

            var response = new
            {
                walletId = Guid.NewGuid().ToString(),
                walletName = request.WalletName,
                registrationDate = DateTime.UtcNow,
                status = "Active"
            };

            return Ok(response);
        }

        /// <summary>
        /// Get wallet details
        /// </summary>
        [HttpGet("{walletId}")]
        public IActionResult GetWallet(string walletId)
        {
            if (string.IsNullOrEmpty(walletId))
            {
                return BadRequest(new { message = "Wallet ID is required" });
            }

            if (walletId == "invalid-wallet-id")
            {
                return NotFound(new { message = "Wallet not found" });
            }

            var response = new
            {
                walletId = walletId,
                walletName = "Test Wallet",
                registrationDate = DateTime.UtcNow.AddDays(-30),
                status = "Active",
                balance = new
                {
                    amount = 1000.00m,
                    currency = "EUR"
                }
            };

            return Ok(response);
        }

        /// <summary>
        /// Get wallet balance
        /// </summary>
        [HttpGet("{walletId}/balance")]
        public IActionResult GetWalletBalance(string walletId)
        {
            if (string.IsNullOrEmpty(walletId))
            {
                return BadRequest(new { message = "Wallet ID is required" });
            }

            var response = new
            {
                walletId = walletId,
                balance = new
                {
                    amount = 1000.00m,
                    currency = "EUR",
                    lastUpdated = DateTime.UtcNow
                }
            };

            return Ok(response);
        }

        /// <summary>
        /// Search wallets
        /// </summary>
        [HttpGet("search")]
        public IActionResult SearchWallets([FromQuery] string? name = null)
        {
            var wallets = new[]
            {
                new
                {
                    walletId = Guid.NewGuid().ToString(),
                    walletName = "Test Wallet 1",
                    status = "Active"
                },
                new
                {
                    walletId = Guid.NewGuid().ToString(),
                    walletName = "Test Wallet 2",
                    status = "Active"
                }
            };

            return Ok(new { wallets });
        }

        /// <summary>
        /// Load funds to wallet
        /// </summary>
        [HttpPost("{walletId}/load")]
        public IActionResult LoadWallet(string walletId, [FromBody] LoadWalletRequest request)
        {
            if (string.IsNullOrEmpty(walletId))
            {
                return BadRequest(new { message = "Wallet ID is required" });
            }

            var response = new
            {
                transactionId = Guid.NewGuid().ToString(),
                walletId = walletId,
                amount = request.Amount,
                currency = request.Currency,
                status = "Completed",
                timestamp = DateTime.UtcNow
            };

            return Ok(response);
        }

        /// <summary>
        /// Unload funds from wallet
        /// </summary>
        [HttpPost("{walletId}/unload")]
        public IActionResult UnloadWallet(string walletId, [FromBody] UnloadWalletRequest request)
        {
            if (string.IsNullOrEmpty(walletId))
            {
                return BadRequest(new { message = "Wallet ID is required" });
            }

            var response = new
            {
                transactionId = Guid.NewGuid().ToString(),
                walletId = walletId,
                amount = request.Amount,
                currency = request.Currency,
                status = "Completed",
                timestamp = DateTime.UtcNow
            };

            return Ok(response);
        }

        /// <summary>
        /// Edit wallet
        /// </summary>
        [HttpPut("{walletId}")]
        public IActionResult EditWallet(string walletId, [FromBody] EditWalletRequest request)
        {
            if (string.IsNullOrEmpty(walletId))
            {
                return BadRequest(new { message = "Wallet ID is required" });
            }

            var response = new
            {
                walletId = walletId,
                walletName = request.WalletName,
                status = "Active",
                lastModified = DateTime.UtcNow
            };

            return Ok(response);
        }
    }

    public class WalletRegisterRequest
    {
        [Required]
        public string WalletName { get; set; } = string.Empty;
    }

    public class LoadWalletRequest
    {
        [Required]
        public decimal Amount { get; set; }
        
        [Required]
        public string Currency { get; set; } = "EUR";
    }

    public class UnloadWalletRequest
    {
        [Required]
        public decimal Amount { get; set; }
        
        [Required]
        public string Currency { get; set; } = "EUR";
    }

    public class EditWalletRequest
    {
        [Required]
        public string WalletName { get; set; } = string.Empty;
    }
}
