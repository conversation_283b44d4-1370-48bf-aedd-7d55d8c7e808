using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text;

namespace SandboxTest.Infrastructure
{
    /// <summary>
    /// Factory for creating HTTP client to connect to the NBG eWallet Sandbox API
    /// </summary>
    public class SandboxApiFactory : IDisposable
    {
        private readonly string _sandboxId;
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly string _baseUrl;

        public SandboxApiFactory()
        {
            _sandboxId = Guid.NewGuid().ToString();
            // Use the NBG public sandbox API instead of local
            _baseUrl = "https://apis.nbg.gr/sandbox/ewallet/v1";

            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new Uri(_baseUrl);

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            };
            _jsonOptions.Converters.Add(new JsonStringEnumConverter());
        }

        public string SandboxId => _sandboxId;

        /// <summary>
        /// Creates an HTTP client configured for the NBG Sandbox API
        /// </summary>
        public HttpClient CreateClient()
        {
            var client = new HttpClient();
            client.BaseAddress = new Uri(_baseUrl);

            // Add required headers for NBG Sandbox API
            // Note: In a real scenario, you would need actual API credentials
            client.DefaultRequestHeaders.Add("X-IBM-Client-Id", "your-client-id-here");
            client.DefaultRequestHeaders.Add("X-IBM-Client-Secret", "your-client-secret-here");
            client.DefaultRequestHeaders.Add("sandbox-id", _sandboxId);
            client.DefaultRequestHeaders.Add("Accept", "application/json");

            return client;
        }

        /// <summary>
        /// Check if sandbox exists and has data
        /// </summary>
        public async Task<bool> CheckSandboxExistsAsync()
        {
            try
            {
                using var client = CreateClient();
                var response = await client.GetAsync($"/sandbox/{_sandboxId}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Warning: Could not check sandbox existence: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Loads sandbox data from SandboxImport.json - only call this when needed!
        /// </summary>
        public async Task LoadSandboxDataAsync()
        {
            try
            {
                Console.WriteLine($"🔄 Loading sandbox data for sandbox ID: {_sandboxId}");

                // Load the sandbox import data
                var importDataPath = Path.Combine("Assets", "SandboxImport.json");
                if (!File.Exists(importDataPath))
                {
                    throw new FileNotFoundException($"Sandbox import file not found: {importDataPath}");
                }

                var importData = await File.ReadAllTextAsync(importDataPath);

                // Add test marker to the import data
                var testMarkerGuid = "*************-9999-9999-************";
                var modifiedImportData = AddTestMarkerToImportData(importData, testMarkerGuid);

                var content = new StringContent(modifiedImportData, Encoding.UTF8, "application/json");

                using var client = CreateClient();
                var response = await client.PostAsync("/sandbox/import", content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new InvalidOperationException($"Failed to load sandbox data: {response.StatusCode} - {errorContent}");
                }

                Console.WriteLine($"✅ Sandbox data loaded successfully for sandbox ID: {_sandboxId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to load sandbox data: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Add test marker to the import data to indicate data has been loaded
        /// </summary>
        private string AddTestMarkerToImportData(string importData, string testMarkerGuid)
        {
            try
            {
                // Parse the existing import data
                var jsonDoc = JsonDocument.Parse(importData);
                var root = jsonDoc.RootElement;

                // Create a new object with the test marker added to the ewalletSandbox section
                using var stream = new MemoryStream();
                using var writer = new Utf8JsonWriter(stream);

                writer.WriteStartObject();

                // Copy all existing root properties
                foreach (var property in root.EnumerateObject())
                {
                    if (property.Name == "ewalletSandbox")
                    {
                        // Add test marker to the ewalletSandbox section
                        writer.WritePropertyName("ewalletSandbox");
                        writer.WriteStartObject();

                        // Add test marker first
                        writer.WritePropertyName("testMarker");
                        writer.WriteStartObject();
                        writer.WriteString("id", testMarkerGuid);
                        writer.WriteString("name", "TEST_MARKER_DO_NOT_DELETE");
                        writer.WriteString("description", "Indicates sandbox data has been loaded");
                        writer.WriteString("createdAt", DateTime.UtcNow.ToString("O"));
                        writer.WriteString("version", "1.0");
                        writer.WriteEndObject();

                        // Copy all existing ewalletSandbox properties
                        foreach (var ewalletProperty in property.Value.EnumerateObject())
                        {
                            ewalletProperty.WriteTo(writer);
                        }

                        writer.WriteEndObject();
                    }
                    else
                    {
                        // Copy other root properties as-is
                        property.WriteTo(writer);
                    }
                }

                writer.WriteEndObject();
                writer.Flush();

                return Encoding.UTF8.GetString(stream.ToArray());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Could not add test marker to import data: {ex.Message}");
                return importData; // Return original data if modification fails
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
