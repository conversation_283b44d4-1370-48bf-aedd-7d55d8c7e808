using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text;

namespace SandboxTest.Infrastructure
{
    /// <summary>
    /// Factory for creating HTTP client to connect to the NBG eWallet Sandbox API
    /// </summary>
    public class SandboxApiFactory : IDisposable
    {
        private readonly string _sandboxId;
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly string _baseUrl;

        public SandboxApiFactory()
        {
            _sandboxId = Guid.NewGuid().ToString();
            _baseUrl = "https://localhost:5001"; // Local Sandbox API base URL

            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new Uri(_baseUrl);

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            };
            _jsonOptions.Converters.Add(new JsonStringEnumConverter());
        }

        public string SandboxId => _sandboxId;

        /// <summary>
        /// Creates an HTTP client configured for the local Sandbox API
        /// </summary>
        public HttpClient CreateClient()
        {
            var client = new HttpClient();
            client.BaseAddress = new Uri(_baseUrl);

            // Add required headers for local Sandbox API
            client.DefaultRequestHeaders.Add("sandboxId", _sandboxId);
            client.DefaultRequestHeaders.Add("Accept", "application/json");

            return client;
        }

        /// <summary>
        /// Initializes the sandbox with test data from SandboxImport.json
        /// </summary>
        public async Task InitializeSandboxAsync()
        {
            try
            {
                // Load the sandbox import data
                var importDataPath = Path.Combine("Assets", "SandboxImport.json");
                if (File.Exists(importDataPath))
                {
                    var importData = await File.ReadAllTextAsync(importDataPath);
                    var content = new StringContent(importData, Encoding.UTF8, "application/json");

                    using var client = CreateClient();
                    var response = await client.PostAsync("/sandbox/import", content);

                    if (!response.IsSuccessStatusCode)
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        throw new InvalidOperationException($"Failed to initialize sandbox: {response.StatusCode} - {errorContent}");
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the test setup
                Console.WriteLine($"Warning: Could not initialize sandbox data: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
