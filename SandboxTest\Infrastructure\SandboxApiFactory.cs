using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Nbg.Ewallet.Api.Sandbox;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace SandboxTest.Infrastructure
{
    /// <summary>
    /// Factory for creating a test server for the Sandbox API
    /// </summary>
    public class SandboxApiFactory : WebApplicationFactory<Program>
    {
        private readonly string _sandboxId;
        private readonly JsonSerializerOptions _jsonOptions;

        public SandboxApiFactory()
        {
            _sandboxId = Guid.NewGuid().ToString();
            
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            };
            _jsonOptions.Converters.Add(new JsonStringEnumConverter());
        }

        public string SandboxId => _sandboxId;

        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.Testing.json", optional: false);
            });

            builder.UseEnvironment("Testing");
            
            builder.ConfigureServices(services =>
            {
                // Add any mock services here if needed
            });
        }

        /// <summary>
        /// Initializes the sandbox with test data
        /// </summary>
        public async Task InitializeSandboxAsync()
        {
            using var client = CreateClient();
            
            var requestPayload = new
            {
                sandboxId = _sandboxId
            };

            var response = await client.PostAsJsonAsync("sandbox", requestPayload);
            response.EnsureSuccessStatusCode();
        }
    }
}
