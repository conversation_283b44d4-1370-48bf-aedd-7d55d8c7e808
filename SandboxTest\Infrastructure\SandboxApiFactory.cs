using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Collections.Generic;

namespace SandboxTest.Infrastructure
{
    /// <summary>
    /// Factory for creating a test server for the Sandbox API
    /// </summary>
    public class SandboxApiFactory : WebApplicationFactory<SandboxApiFactory>
    {
        private readonly string _sandboxId;
        private readonly JsonSerializerOptions _jsonOptions;

        public SandboxApiFactory()
        {
            _sandboxId = Guid.NewGuid().ToString();
            
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            };
            _jsonOptions.Converters.Add(new JsonStringEnumConverter());
        }

        public string SandboxId => _sandboxId;

        protected override IHostBuilder CreateHostBuilder()
        {
            return Host.CreateDefaultBuilder()
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseEnvironment("Testing");
                    webBuilder.UseStartup<TestStartup>();

                    webBuilder.ConfigureAppConfiguration((context, config) =>
                    {
                        config.AddInMemoryCollection(new Dictionary<string, string>
                        {
                            {"Logging:LogLevel:Default", "Warning"},
                            {"AllowedHosts", "*"}
                        });
                    });
                });
        }

        /// <summary>
        /// Initializes the sandbox with test data
        /// </summary>
        public async Task InitializeSandboxAsync()
        {
            // For now, we'll skip sandbox initialization to avoid dependencies
            // In a real implementation, this would set up test data
            await Task.CompletedTask;
        }
    }
}
