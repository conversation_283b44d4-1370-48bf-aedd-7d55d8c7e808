using FluentAssertions;
using SandboxTest.Infrastructure;
using System.Net;
using System.Text;
using System.Text.Json;
using Xunit;

namespace SandboxTest.Tests
{
    /// <summary>
    /// Comprehensive tests for Wallet functionality in the NBG eWallet Sandbox API
    ///
    /// Test Logic Overview:
    /// This test class covers all wallet-related operations using actual REST API calls:
    /// 1. Wallet Registration - Creating new wallets with validation
    /// 2. Wallet Information Retrieval - Getting wallet details by ID
    /// 3. Wallet Search - Finding wallets by various criteria
    /// 4. Wallet Balance - Checking wallet balance information
    /// 5. Wallet Load/Unload - Adding/removing funds from wallets
    /// 6. Wallet Edit - Modifying wallet properties
    ///
    /// Each test follows the pattern:
    /// - Arrange: Set up test data and preconditions
    /// - Act: Execute the actual REST API call
    /// - Assert: Verify the response and side effects
    /// - Verify: Confirm the operation's impact on the system (end-to-end)
    /// </summary>
    public class WalletTests : SandboxTestBase
    {
        [Fact(DisplayName = "Initialize Sandbox - Should import test data successfully")]
        public async Task InitializeSandbox_ShouldImportTestData_Successfully()
        {
            // Arrange
            /* Test Logic:
             * 1. Load the SandboxImport.json file
             * 2. Call the sandbox import endpoint
             * 3. Verify the import was successful
             *
             * Expected Result:
             * - Sandbox data is imported successfully
             * - API returns success status
             */

            // Act - Initialize sandbox with test data
            await Factory.InitializeSandboxAsync();

            // Assert - If we get here without exception, initialization was successful
            Assert.True(true, "Sandbox initialization completed successfully");
        }

        [Fact(DisplayName = "Register Wallet - Should create a new wallet and return wallet details")]
        public async Task RegisterWallet_ShouldCreateNewWallet_AndReturnWalletDetails()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a new wallet with valid Greek business data
             * 2. Verify the API returns success and wallet details
             * 3. Verify the wallet can be retrieved using wallet info endpoint
             *
             * Expected Result:
             * - Wallet is created with the specified details
             * - Wallet has a valid ID and account information
             * - Wallet is retrievable via the wallet info endpoint
             */
            var walletName = $"Test Business Wallet {DateTime.Now:yyyyMMddHHmmss}";
            var walletData = new
            {
                walletName = walletName,
                organizationName = "Test Organization Ltd",
                businessType = "CORPORATION",
                contactEmail = "<EMAIL>",
                contactPhone = "+***********",
                address = new
                {
                    street = "Syntagma Square 1",
                    city = "Athens",
                    postalCode = "10563",
                    country = "GR"
                }
            };

            var json = JsonSerializer.Serialize(walletData);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act - Call the actual REST API endpoint for wallet registration
            var registerResponse = await Client.PostAsync("/wallet/register", content);

            // Assert - Verify wallet creation was successful
            registerResponse.StatusCode.Should().Be(HttpStatusCode.OK, "wallet registration should succeed");

            var responseContent = await registerResponse.Content.ReadAsStringAsync();
            var wallet = JsonSerializer.Deserialize<JsonElement>(responseContent);

            wallet.GetProperty("walletId").GetString().Should().NotBeNullOrEmpty("wallet should have a valid ID");
            wallet.GetProperty("walletName").GetString().Should().Be(walletName, "wallet name should match the requested name");

            // Verify - Confirm wallet can be retrieved using wallet info endpoint
            var walletId = wallet.GetProperty("walletId").GetString();
            var getWalletResponse = await Client.GetAsync($"/wallet/{walletId}/info");
            getWalletResponse.StatusCode.Should().Be(HttpStatusCode.OK, "wallet should be retrievable by ID");

            var retrievedContent = await getWalletResponse.Content.ReadAsStringAsync();
            var retrievedWallet = JsonSerializer.Deserialize<JsonElement>(retrievedContent);

            retrievedWallet.GetProperty("walletId").GetString().Should().Be(walletId, "retrieved wallet should have the same ID");
            retrievedWallet.GetProperty("walletName").GetString().Should().Be(walletName, "retrieved wallet should have the same name");
        }

        [Fact(DisplayName = "Register Wallet - Should fail with empty wallet name")]
        public async Task RegisterWallet_ShouldFail_WithEmptyWalletName()
        {
            // Arrange
            /* Test Logic:
             * 1. Attempt to create a wallet with an empty name
             * 2. Verify the API returns a validation error
             *
             * Expected Result:
             * - API returns 400 Bad Request
             * - Error message indicates invalid wallet name
             */
            var walletData = new
            {
                walletName = "", // Empty wallet name should cause validation error
                organizationName = "Test Organization Ltd",
                businessType = "CORPORATION",
                contactEmail = "<EMAIL>",
                contactPhone = "+***********"
            };

            var json = JsonSerializer.Serialize(walletData);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act - Call the actual REST API endpoint for wallet registration
            var registerResponse = await Client.PostAsync("/wallet/register", content);

            // Assert - Verify validation error is returned
            registerResponse.StatusCode.Should().Be(HttpStatusCode.BadRequest, "empty wallet name should cause validation error");
        }

        [Fact(DisplayName = "Get Wallet - Should return wallet details for valid wallet ID")]
        public async Task GetWallet_ShouldReturnWalletDetails_ForValidWalletId()
        {
            // Arrange
            /* Test Logic:
             * 1. First create a wallet to ensure we have a valid ID
             * 2. Retrieve the wallet using its ID
             * 3. Verify all wallet details are returned correctly
             *
             * Expected Result:
             * - Wallet details match the created wallet
             * - All required fields are populated
             */
            var walletName = $"Test Wallet {DateTime.Now:yyyyMMddHHmmss}";
            var walletData = new
            {
                walletName = walletName,
                organizationName = "Test Organization Ltd",
                businessType = "CORPORATION",
                contactEmail = "<EMAIL>",
                contactPhone = "+***********"
            };

            var json = JsonSerializer.Serialize(walletData);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var registerResponse = await Client.PostAsync("/wallet/register", content);
            registerResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            var registerContent = await registerResponse.Content.ReadAsStringAsync();
            var createdWallet = JsonSerializer.Deserialize<JsonElement>(registerContent);

            // Act - Call the actual REST API endpoint to get wallet details
            var walletId = createdWallet.GetProperty("walletId").GetString();
            var getWalletResponse = await Client.GetAsync($"/wallet/{walletId}/info");

            // Assert - Verify wallet details are returned correctly
            getWalletResponse.StatusCode.Should().Be(HttpStatusCode.OK, "wallet should be retrievable by ID");

            var walletContent = await getWalletResponse.Content.ReadAsStringAsync();
            var wallet = JsonSerializer.Deserialize<JsonElement>(walletContent);

            wallet.GetProperty("walletId").GetString().Should().Be(walletId, "wallet ID should match");
            wallet.GetProperty("walletName").GetString().Should().Be(walletName, "wallet name should match");
        }

        [Fact(DisplayName = "Get Wallet - Should return not found for invalid wallet ID")]
        public async Task GetWallet_ShouldReturnNotFound_ForInvalidWalletId()
        {
            // Arrange
            /* Test Logic:
             * 1. Use a random GUID that doesn't exist in the system
             * 2. Attempt to retrieve wallet information
             * 3. Verify the API returns 404 Not Found
             * 
             * Expected Result:
             * - API returns 404 Not Found
             * - Error message indicates wallet not found
             */
            var invalidWalletId = Guid.NewGuid();

            // Act
            var getWalletResponse = await Client.GetAsync($"wallet/{invalidWalletId}/info");

            // Assert
            await AssertErrorResponse(getWalletResponse, HttpStatusCode.NotFound);
        }

        [Fact(DisplayName = "Get Wallet Balance - Should return balance information for valid wallet")]
        public async Task GetWalletBalance_ShouldReturnBalanceInformation_ForValidWallet()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet to ensure we have a valid wallet
             * 2. Retrieve the wallet balance
             * 3. Verify balance information is returned with correct format
             *
             * Expected Result:
             * - Balance is returned as a decimal value
             * - Currency is specified (EUR)
             * - Last updated timestamp is provided
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Act
            var balanceResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");

            // Assert
            var balance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(balanceResponse);

            balance.Balance.Should().BeGreaterOrEqualTo(0, "balance should be non-negative");
            balance.Currency.Should().Be("EUR", "currency should be EUR");
            balance.LastUpdated.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(5), "last updated should be recent");
        }

        [Fact(DisplayName = "Search Wallets - Should return wallets matching search criteria")]
        public async Task SearchWallets_ShouldReturnWallets_MatchingSearchCriteria()
        {
            // Arrange
            /* Test Logic:
             * 1. Create multiple wallets with different properties
             * 2. Search for wallets using various criteria (name, VAT, organization)
             * 3. Verify search results contain only matching wallets
             *
             * Expected Result:
             * - Search returns only wallets matching the criteria
             * - Search results include all required wallet information
             * - Empty search criteria returns all wallets
             */
            var wallet1Name = TestDataHelper.GenerateUniqueWalletName();
            var wallet2Name = TestDataHelper.GenerateUniqueWalletName();

            var registerRequest1 = TestDataHelper.CreateWalletRegistrationRequest(wallet1Name);
            var registerRequest2 = TestDataHelper.CreateWalletRegistrationRequest(wallet2Name);

            var registerResponse1 = await Client.PostAsJsonAsync("wallet/register", registerRequest1);
            var registerResponse2 = await Client.PostAsJsonAsync("wallet/register", registerRequest2);

            var createdWallet1 = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse1);
            var createdWallet2 = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse2);

            // Act - Search by wallet name
            var searchResponse = await Client.GetAsync($"wallet/search?walletName={wallet1Name}");

            // Assert
            var searchResults = await AssertSuccessAndDeserialize<WalletSearchResponse>(searchResponse);

            searchResults.Wallets.Should().NotBeEmpty("search should return results");
            searchResults.Wallets.Should().Contain(w => w.WalletName == wallet1Name, "search should include the matching wallet");
            searchResults.Wallets.Should().NotContain(w => w.WalletName == wallet2Name, "search should not include non-matching wallets");
        }

        [Fact(DisplayName = "Load Wallet - Should add funds to wallet and update balance")]
        public async Task LoadWallet_ShouldAddFunds_AndUpdateBalance()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and get initial balance
             * 2. Load funds into the wallet
             * 3. Verify the load operation succeeds
             * 4. Verify the wallet balance is updated correctly
             *
             * Expected Result:
             * - Load operation returns success
             * - Wallet balance increases by the loaded amount
             * - Transaction is recorded in wallet statements
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Get initial balance
            var initialBalanceResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");
            var initialBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(initialBalanceResponse);

            var loadAmount = TestDataHelper.GenerateAmount(100, 500);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(loadAmount);

            // Act
            var loadResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Assert
            loadResponse.StatusCode.Should().Be(HttpStatusCode.OK, "load operation should succeed");

            // Verify balance update
            var updatedBalanceResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");
            var updatedBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(updatedBalanceResponse);

            updatedBalance.Balance.Should().Be(initialBalance.Balance + loadAmount, "balance should increase by loaded amount");
        }

        [Fact(DisplayName = "Unload Wallet - Should remove funds from wallet and update balance")]
        public async Task UnloadWallet_ShouldRemoveFunds_AndUpdateBalance()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and load initial funds
             * 2. Unload a portion of the funds
             * 3. Verify the unload operation succeeds
             * 4. Verify the wallet balance is updated correctly
             *
             * Expected Result:
             * - Unload operation returns success
             * - Wallet balance decreases by the unloaded amount
             * - Cannot unload more than available balance
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Load initial funds
            var initialLoadAmount = TestDataHelper.GenerateAmount(500, 1000);
            var loadRequest = TestDataHelper.CreateWalletLoadRequest(initialLoadAmount);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/load", loadRequest);

            // Get balance after loading
            var balanceAfterLoadResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");
            var balanceAfterLoad = await AssertSuccessAndDeserialize<WalletBalanceResponse>(balanceAfterLoadResponse);

            var unloadAmount = TestDataHelper.GenerateAmount(100, 300);
            var unloadRequest = TestDataHelper.CreateWalletLoadRequest(unloadAmount);

            // Act
            var unloadResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/unload", unloadRequest);

            // Assert
            unloadResponse.StatusCode.Should().Be(HttpStatusCode.OK, "unload operation should succeed");

            // Verify balance update
            var updatedBalanceResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/balance");
            var updatedBalance = await AssertSuccessAndDeserialize<WalletBalanceResponse>(updatedBalanceResponse);

            updatedBalance.Balance.Should().Be(balanceAfterLoad.Balance - unloadAmount, "balance should decrease by unloaded amount");
        }

        [Fact(DisplayName = "Edit Wallet - Should update wallet name successfully")]
        public async Task EditWallet_ShouldUpdateWalletName_Successfully()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet with an initial name
             * 2. Update the wallet name using the edit endpoint
             * 3. Verify the edit operation succeeds
             * 4. Verify the wallet name is updated when retrieved
             *
             * Expected Result:
             * - Edit operation returns success
             * - Wallet name is updated to the new value
             * - Other wallet properties remain unchanged
             */
            var originalWalletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(originalWalletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            var newWalletName = TestDataHelper.GenerateUniqueWalletName();
            var editRequest = new { WalletName = newWalletName };

            // Act
            var editResponse = await Client.PutAsJsonAsync($"wallet/{createdWallet.WalletId}/edit", editRequest);

            // Assert
            editResponse.StatusCode.Should().Be(HttpStatusCode.OK, "edit operation should succeed");

            // Verify the wallet name was updated
            var updatedWalletResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/info");
            var updatedWallet = await AssertSuccessAndDeserialize<WalletResponse>(updatedWalletResponse);

            updatedWallet.WalletName.Should().Be(newWalletName, "wallet name should be updated");
            updatedWallet.WalletId.Should().Be(createdWallet.WalletId, "wallet ID should remain unchanged");
            updatedWallet.WalletAccount.Should().Be(createdWallet.WalletAccount, "wallet account should remain unchanged");
        }
    }
}
