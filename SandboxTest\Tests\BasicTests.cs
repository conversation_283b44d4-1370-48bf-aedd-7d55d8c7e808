using System;
using System.Text.Json;
using Xunit;
using FluentAssertions;

namespace SandboxTest.Tests
{
    /// <summary>
    /// Basic tests that don't require any external dependencies
    /// </summary>
    public class BasicTests
    {
        [Fact(DisplayName = "Basic Test - Should pass simple assertion")]
        public void BasicTest_ShouldPass_SimpleAssertion()
        {
            // Test Logic:
            // 1. Perform a simple assertion to verify test framework is working
            // 2. This test should always pass
            //
            // Expected Result:
            // - Test passes successfully

            var result = 1 + 1;
            result.Should().Be(2);
        }

        [Fact(DisplayName = "JSON Serialization Test - Should serialize and deserialize object")]
        public void JsonSerializationTest_ShouldSerializeAndDeserialize_Object()
        {
            // Test Logic:
            // 1. Create a simple object
            // 2. Serialize it to JSON
            // 3. Deserialize it back to object
            // 4. Verify the data is preserved
            //
            // Expected Result:
            // - Object should be serialized and deserialized correctly

            var originalObject = new { Name = "Test", Value = 42 };
            
            var json = JsonSerializer.Serialize(originalObject);
            json.Should().NotBeNullOrEmpty();
            
            var deserializedObject = JsonSerializer.Deserialize<dynamic>(json);
            deserializedObject.Should().NotBeNull();
        }

        [Fact(DisplayName = "String Test - Should manipulate strings correctly")]
        public void StringTest_ShouldManipulateStrings_Correctly()
        {
            // Test Logic:
            // 1. Test basic string operations
            // 2. Verify string concatenation and formatting
            //
            // Expected Result:
            // - String operations should work as expected

            var firstName = "John";
            var lastName = "Doe";
            var fullName = $"{firstName} {lastName}";
            
            fullName.Should().Be("John Doe");
            fullName.Should().Contain("John");
            fullName.Should().Contain("Doe");
        }
    }
}
