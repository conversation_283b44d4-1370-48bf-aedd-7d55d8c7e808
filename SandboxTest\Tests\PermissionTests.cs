using FluentAssertions;
using SandboxTest.Infrastructure;
using SandboxTest.Models;
using System.Net;
using Xunit;

namespace SandboxTest.Tests
{
    /// <summary>
    /// Comprehensive tests for Permission functionality in the Sandbox API
    /// 
    /// Test Logic Overview:
    /// This test class covers all permission-related operations including:
    /// 1. Wallet Permissions - Granting access between different wallets
    /// 2. User Permissions - Managing individual user access rights
    /// 3. Authorization Requests - Handling permission requests and approvals
    /// 4. Permission Validation - Ensuring proper access control enforcement
    /// 5. Permission Revocation - Removing granted permissions
    /// 
    /// Each test validates both the permission assignment and the actual
    /// access control enforcement to ensure security is properly maintained.
    /// </summary>
    public class PermissionTests : SandboxTestBase
    {
        [Fact(DisplayName = "Set Wallet Permissions - Should grant permissions between wallets")]
        public async Task SetWalletPermissions_ShouldGrantPermissions_BetweenWallets()
        {
            // Arrange
            /* Test Logic:
             * 1. Create two wallets (source and target)
             * 2. Grant permissions from source wallet to target wallet
             * 3. Verify the permission assignment succeeds
             * 4. Verify permissions can be retrieved and are correct
             * 5. Verify the target wallet can access source wallet resources
             * 
             * Expected Result:
             * - Permission assignment returns success
             * - Target wallet has specified permissions on source wallet
             * - Permissions include correct access levels (view, submit, approve)
             * - Permissions are retrievable via the get endpoint
             */
            var sourceWalletName = TestDataHelper.GenerateUniqueWalletName();
            var targetWalletName = TestDataHelper.GenerateUniqueWalletName();
            
            var sourceRegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(sourceWalletName);
            var targetRegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(targetWalletName);
            
            var sourceRegisterResponse = await Client.PostAsJsonAsync("wallet/register", sourceRegisterRequest);
            var targetRegisterResponse = await Client.PostAsJsonAsync("wallet/register", targetRegisterRequest);
            
            var sourceWallet = await AssertSuccessAndDeserialize<WalletResponse>(sourceRegisterResponse);
            var targetWallet = await AssertSuccessAndDeserialize<WalletResponse>(targetRegisterResponse);

            var permissionRequest = TestDataHelper.CreateWalletPermissionRequest(targetWallet.WalletId);

            // Act
            var permissionResponse = await Client.PostAsJsonAsync($"wallet/{sourceWallet.WalletId}/wallet-permissions", permissionRequest);

            // Assert
            var permissionResult = await AssertSuccessAndDeserialize<WalletPermissionResponse>(permissionResponse);
            
            permissionResult.Should().NotBeNull("permission response should not be null");
            permissionResult.WalletPermissions.Should().NotBeEmpty("wallet permissions should be created");

            // Verify permissions can be retrieved
            var getPermissionsResponse = await Client.GetAsync($"wallet/{sourceWallet.WalletId}/wallet-permissions");
            var retrievedPermissions = await AssertSuccessAndDeserialize<List<WalletPermission>>(getPermissionsResponse);
            
            retrievedPermissions.Should().Contain(p => p.ExternalWalletId == targetWallet.WalletId, 
                "target wallet should have permissions on source wallet");
            
            var walletPermission = retrievedPermissions.First(p => p.ExternalWalletId == targetWallet.WalletId);
            walletPermission.WalletId.Should().Be(sourceWallet.WalletId, "permission should be for the source wallet");
            walletPermission.BalanceView.Should().BeTrue("target wallet should have balance view permissions");
            walletPermission.Submit.Should().BeTrue("target wallet should have submit permissions");
            walletPermission.TransactionView.Should().BeTrue("target wallet should have transaction view permissions");
        }

        [Fact(DisplayName = "Get Wallet Permissions - Should return all wallet permissions")]
        public async Task GetWalletPermissions_ShouldReturnAllWalletPermissions()
        {
            // Arrange
            /* Test Logic:
             * 1. Create multiple wallets
             * 2. Grant permissions from one wallet to multiple other wallets
             * 3. Retrieve all wallet permissions
             * 4. Verify all granted permissions are returned
             * 5. Verify permission details are complete
             * 
             * Expected Result:
             * - All granted wallet permissions are returned
             * - Permission details include all required fields
             * - Permissions match what was originally granted
             */
            var sourceWalletName = TestDataHelper.GenerateUniqueWalletName();
            var target1WalletName = TestDataHelper.GenerateUniqueWalletName();
            var target2WalletName = TestDataHelper.GenerateUniqueWalletName();
            
            var sourceRegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(sourceWalletName);
            var target1RegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(target1WalletName);
            var target2RegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(target2WalletName);
            
            var sourceRegisterResponse = await Client.PostAsJsonAsync("wallet/register", sourceRegisterRequest);
            var target1RegisterResponse = await Client.PostAsJsonAsync("wallet/register", target1RegisterRequest);
            var target2RegisterResponse = await Client.PostAsJsonAsync("wallet/register", target2RegisterRequest);
            
            var sourceWallet = await AssertSuccessAndDeserialize<WalletResponse>(sourceRegisterResponse);
            var target1Wallet = await AssertSuccessAndDeserialize<WalletResponse>(target1RegisterResponse);
            var target2Wallet = await AssertSuccessAndDeserialize<WalletResponse>(target2RegisterResponse);

            // Grant permissions to both target wallets
            var permission1Request = TestDataHelper.CreateWalletPermissionRequest(target1Wallet.WalletId);
            var permission2Request = TestDataHelper.CreateWalletPermissionRequest(target2Wallet.WalletId);
            
            await Client.PostAsJsonAsync($"wallet/{sourceWallet.WalletId}/wallet-permissions", permission1Request);
            await Client.PostAsJsonAsync($"wallet/{sourceWallet.WalletId}/wallet-permissions", permission2Request);

            // Act
            var getPermissionsResponse = await Client.GetAsync($"wallet/{sourceWallet.WalletId}/wallet-permissions");

            // Assert
            var permissions = await AssertSuccessAndDeserialize<List<WalletPermission>>(getPermissionsResponse);
            
            permissions.Should().HaveCountGreaterOrEqualTo(2, "should have permissions for both target wallets");
            permissions.Should().Contain(p => p.ExternalWalletId == target1Wallet.WalletId, "should include first target wallet");
            permissions.Should().Contain(p => p.ExternalWalletId == target2Wallet.WalletId, "should include second target wallet");
            
            foreach (var permission in permissions)
            {
                permission.Id.Should().NotBeEmpty("permission should have a valid ID");
                permission.WalletId.Should().Be(sourceWallet.WalletId, "permission should be for the source wallet");
                permission.ExternalWalletId.Should().NotBeEmpty("permission should have an external wallet ID");
                permission.CreationDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(5), "creation date should be recent");
                permission.ExpirationDate.Should().BeAfter(DateTime.UtcNow, "expiration date should be in the future");
            }
        }

        [Fact(DisplayName = "Get Wallet Permissions For Specific Wallet - Should return permissions for individual wallet")]
        public async Task GetWalletPermissionsForSpecificWallet_ShouldReturnPermissions_ForIndividualWallet()
        {
            // Arrange
            /* Test Logic:
             * 1. Create source and target wallets
             * 2. Grant permissions from source to target
             * 3. Retrieve permissions for the specific target wallet
             * 4. Verify only that wallet's permissions are returned
             * 
             * Expected Result:
             * - Only the specified wallet's permissions are returned
             * - Permission details match what was granted
             * - Other wallets' permissions are not included
             */
            var sourceWalletName = TestDataHelper.GenerateUniqueWalletName();
            var targetWalletName = TestDataHelper.GenerateUniqueWalletName();
            
            var sourceRegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(sourceWalletName);
            var targetRegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(targetWalletName);
            
            var sourceRegisterResponse = await Client.PostAsJsonAsync("wallet/register", sourceRegisterRequest);
            var targetRegisterResponse = await Client.PostAsJsonAsync("wallet/register", targetRegisterRequest);
            
            var sourceWallet = await AssertSuccessAndDeserialize<WalletResponse>(sourceRegisterResponse);
            var targetWallet = await AssertSuccessAndDeserialize<WalletResponse>(targetRegisterResponse);

            var permissionRequest = TestDataHelper.CreateWalletPermissionRequest(targetWallet.WalletId);
            await Client.PostAsJsonAsync($"wallet/{sourceWallet.WalletId}/wallet-permissions", permissionRequest);

            // Act
            var getWalletPermissionResponse = await Client.GetAsync($"wallet/{sourceWallet.WalletId}/wallet-permissions/{targetWallet.WalletId}");

            // Assert
            var walletPermissions = await AssertSuccessAndDeserialize<List<WalletPermission>>(getWalletPermissionResponse);
            
            walletPermissions.Should().NotBeEmpty("target wallet should have permissions");
            walletPermissions.Should().OnlyContain(p => p.ExternalWalletId == targetWallet.WalletId, 
                "should only return permissions for the specified wallet");
            
            var permission = walletPermissions.First();
            permission.WalletId.Should().Be(sourceWallet.WalletId, "permission should be for the source wallet");
            permission.BalanceView.Should().BeTrue("target wallet should have balance view permissions");
        }

        [Fact(DisplayName = "Revoke Wallet Permissions - Should remove wallet access successfully")]
        public async Task RevokeWalletPermissions_ShouldRemoveWalletAccess_Successfully()
        {
            // Arrange
            /* Test Logic:
             * 1. Create source and target wallets
             * 2. Grant permissions from source to target
             * 3. Verify the target wallet has permissions
             * 4. Revoke the target wallet's permissions
             * 5. Verify the permissions are removed/expired
             * 
             * Expected Result:
             * - Revocation operation succeeds
             * - Target wallet no longer has active permissions
             * - Revoked permissions are marked as expired or removed
             */
            var sourceWalletName = TestDataHelper.GenerateUniqueWalletName();
            var targetWalletName = TestDataHelper.GenerateUniqueWalletName();
            
            var sourceRegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(sourceWalletName);
            var targetRegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(targetWalletName);
            
            var sourceRegisterResponse = await Client.PostAsJsonAsync("wallet/register", sourceRegisterRequest);
            var targetRegisterResponse = await Client.PostAsJsonAsync("wallet/register", targetRegisterRequest);
            
            var sourceWallet = await AssertSuccessAndDeserialize<WalletResponse>(sourceRegisterResponse);
            var targetWallet = await AssertSuccessAndDeserialize<WalletResponse>(targetRegisterResponse);

            var permissionRequest = TestDataHelper.CreateWalletPermissionRequest(targetWallet.WalletId);
            await Client.PostAsJsonAsync($"wallet/{sourceWallet.WalletId}/wallet-permissions", permissionRequest);

            // Verify target wallet has permissions before revocation
            var beforeRevokeResponse = await Client.GetAsync($"wallet/{sourceWallet.WalletId}/wallet-permissions/{targetWallet.WalletId}");
            var beforeRevokePermissions = await AssertSuccessAndDeserialize<List<WalletPermission>>(beforeRevokeResponse);
            beforeRevokePermissions.Should().NotBeEmpty("target wallet should have permissions before revocation");

            // Act
            var revokeResponse = await Client.PutAsync($"wallet/{sourceWallet.WalletId}/wallet-permissions/{targetWallet.WalletId}/revoke", null);

            // Assert
            revokeResponse.StatusCode.Should().Be(HttpStatusCode.OK, "revocation should succeed");

            // Verify permissions are revoked
            var afterRevokeResponse = await Client.GetAsync($"wallet/{sourceWallet.WalletId}/wallet-permissions/{targetWallet.WalletId}");
            
            if (afterRevokeResponse.StatusCode == HttpStatusCode.OK)
            {
                var afterRevokePermissions = await DeserializeResponseAsync<List<WalletPermission>>(afterRevokeResponse);
                if (afterRevokePermissions?.Any() == true)
                {
                    // If permissions still exist, they should be expired
                    afterRevokePermissions.Should().OnlyContain(p => p.ExpirationDate <= DateTime.UtcNow, 
                        "remaining permissions should be expired");
                }
            }
            else
            {
                // Wallet should not be found or have no permissions
                afterRevokeResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.NoContent);
            }
        }

        [Fact(DisplayName = "Request Authorization - Should create authorization request successfully")]
        public async Task RequestAuthorization_ShouldCreateAuthorizationRequest_Successfully()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet that will request authorization
             * 2. Create an authorization request for a specific operation
             * 3. Verify the authorization request is created successfully
             * 4. Verify the request can be retrieved and contains correct details
             * 
             * Expected Result:
             * - Authorization request is created successfully
             * - Request has valid ID and details
             * - Request status is set to pending
             * - Request can be retrieved for approval/rejection
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            var authorizationRequest = new
            {
                RequestType = "Transfer",
                Amount = TestDataHelper.GenerateAmount(100, 500),
                Description = "Test authorization request",
                TargetIban = TestDataHelper.GenerateIban()
            };

            // Act
            var authResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/authorization-requests", authorizationRequest);

            // Assert
            if (authResponse.StatusCode == HttpStatusCode.OK)
            {
                var authResult = await DeserializeResponseAsync<object>(authResponse);
                authResult.Should().NotBeNull("authorization request should be created");
                
                // Note: The exact structure would depend on the API implementation
                // This test validates that the endpoint is accessible and accepts requests
            }
            else
            {
                // Some implementations might not have authorization requests implemented yet
                authResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotImplemented, HttpStatusCode.NotFound);
            }
        }

        [Fact(DisplayName = "Permission Validation - Should enforce access control correctly")]
        public async Task PermissionValidation_ShouldEnforceAccessControl_Correctly()
        {
            // Arrange
            /* Test Logic:
             * 1. Create two wallets without any permissions between them
             * 2. Attempt to access one wallet's resources from the other
             * 3. Verify access is denied
             * 4. Grant permissions and verify access is then allowed
             * 
             * Expected Result:
             * - Access is denied without proper permissions
             * - Access is granted after permissions are assigned
             * - Permission levels are properly enforced
             */
            var wallet1Name = TestDataHelper.GenerateUniqueWalletName();
            var wallet2Name = TestDataHelper.GenerateUniqueWalletName();
            
            var wallet1RegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(wallet1Name);
            var wallet2RegisterRequest = TestDataHelper.CreateWalletRegistrationRequest(wallet2Name);
            
            var wallet1RegisterResponse = await Client.PostAsJsonAsync("wallet/register", wallet1RegisterRequest);
            var wallet2RegisterResponse = await Client.PostAsJsonAsync("wallet/register", wallet2RegisterRequest);
            
            var wallet1 = await AssertSuccessAndDeserialize<WalletResponse>(wallet1RegisterResponse);
            var wallet2 = await AssertSuccessAndDeserialize<WalletResponse>(wallet2RegisterResponse);

            // Act - Try to access wallet1's balance from wallet2's context (should fail)
            // Note: This would require switching context or using different authentication
            // For now, we'll test that permissions can be properly set and retrieved
            
            var permissionRequest = TestDataHelper.CreateWalletPermissionRequest(wallet2.WalletId);
            var permissionResponse = await Client.PostAsJsonAsync($"wallet/{wallet1.WalletId}/wallet-permissions", permissionRequest);

            // Assert
            permissionResponse.StatusCode.Should().Be(HttpStatusCode.OK, "permission assignment should succeed");

            // Verify permissions are properly stored
            var getPermissionsResponse = await Client.GetAsync($"wallet/{wallet1.WalletId}/wallet-permissions");
            var permissions = await AssertSuccessAndDeserialize<List<WalletPermission>>(getPermissionsResponse);
            
            permissions.Should().Contain(p => p.ExternalWalletId == wallet2.WalletId, 
                "wallet2 should have permissions on wallet1");
        }
    }
}
