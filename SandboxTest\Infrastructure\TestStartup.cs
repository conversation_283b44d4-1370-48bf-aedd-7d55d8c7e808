using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace SandboxTest.Infrastructure
{
    /// <summary>
    /// Test startup configuration for the sandbox test environment
    /// </summary>
    public class TestStartup
    {
        public TestStartup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        /// <summary>
        /// Configure services for the test environment
        /// </summary>
        public void ConfigureServices(IServiceCollection services)
        {
            // Add basic services needed for testing
            services.AddControllers();
            services.AddHttpClient();
            
            // Add any additional test-specific services here
        }

        /// <summary>
        /// Configure the HTTP request pipeline for testing
        /// </summary>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseRouting();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
