using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;

namespace SandboxTest.Infrastructure
{
    /// <summary>
    /// Test program entry point for the test web application
    /// </summary>
    public class TestProgram
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<TestStartup>();
                });
    }
}
