using FluentAssertions;
using SandboxTest.Infrastructure;
using SandboxTest.Models;
using System.Net;
using Xunit;

namespace SandboxTest.Tests
{
    /// <summary>
    /// Comprehensive tests for User management functionality in the Sandbox API
    /// 
    /// Test Logic Overview:
    /// This test class covers all user-related operations including:
    /// 1. User Permissions - Managing user access rights to wallets
    /// 2. Available Users - Retrieving users available for permission assignment
    /// 3. Organization Users - Managing company users and their access
    /// 4. User Permission Validation - Ensuring proper access control
    /// 5. User Permission Revocation - Removing user access rights
    /// 
    /// Each test validates both the API response and the actual system state
    /// to ensure operations are properly persisted and accessible.
    /// </summary>
    public class UserTests : SandboxTestBase
    {
        [Fact(DisplayName = "Set User Permissions - Should grant permissions to user successfully")]
        public async Task SetUserPermissions_ShouldGrantPermissions_ToUserSuccessfully()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet to assign permissions for
             * 2. Set user permissions with various access levels
             * 3. Verify the permission assignment succeeds
             * 4. Verify the permissions can be retrieved and are correct
             * 
             * Expected Result:
             * - Permission assignment returns success
             * - User has the specified permissions (admin, approve, etc.)
             * - Limits are properly configured
             * - Permissions are retrievable via the get endpoint
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            var userId = TestDataHelper.GenerateUserId();
            var permissionRequest = TestDataHelper.CreateUserPermissionRequest(userId, createdWallet.WalletId);

            // Act
            var permissionResponse = await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/user-permissions", permissionRequest);

            // Assert
            var permissionResult = await AssertSuccessAndDeserialize<UserPermissionResponse>(permissionResponse);
            
            permissionResult.Should().NotBeNull("permission response should not be null");
            permissionResult.UserPermissions.Should().NotBeEmpty("user permissions should be created");

            // Verify permissions can be retrieved
            var getPermissionsResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/user-permissions");
            var retrievedPermissions = await AssertSuccessAndDeserialize<UserPermissionResponse>(getPermissionsResponse);
            
            retrievedPermissions.UserPermissions.Should().Contain(p => p.UserId == userId, "user should have permissions");
            
            var userPermission = retrievedPermissions.UserPermissions.First(p => p.UserId == userId);
            userPermission.Admin.Should().BeTrue("user should have admin permissions");
            userPermission.Approve.Should().BeTrue("user should have approve permissions");
            userPermission.BalanceView.Should().BeTrue("user should have balance view permissions");
            userPermission.Submit.Should().BeTrue("user should have submit permissions");
            userPermission.TransactionView.Should().BeTrue("user should have transaction view permissions");
            userPermission.Limits.Should().NotBeEmpty("user should have limits configured");
        }

        [Fact(DisplayName = "Get User Permissions - Should return all user permissions for wallet")]
        public async Task GetUserPermissions_ShouldReturnAllUserPermissions_ForWallet()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and assign permissions to multiple users
             * 2. Retrieve all user permissions for the wallet
             * 3. Verify all assigned users are returned
             * 4. Verify permission details are complete and accurate
             * 
             * Expected Result:
             * - All users with permissions are returned
             * - Permission details include all required fields
             * - Permissions match what was originally assigned
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Create permissions for multiple users
            var user1Id = TestDataHelper.GenerateUserId();
            var user2Id = TestDataHelper.GenerateUserId();
            
            var permission1Request = TestDataHelper.CreateUserPermissionRequest(user1Id, createdWallet.WalletId);
            var permission2Request = TestDataHelper.CreateUserPermissionRequest(user2Id, createdWallet.WalletId);
            
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/user-permissions", permission1Request);
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/user-permissions", permission2Request);

            // Act
            var getPermissionsResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/user-permissions");

            // Assert
            var permissions = await AssertSuccessAndDeserialize<UserPermissionResponse>(getPermissionsResponse);
            
            permissions.UserPermissions.Should().HaveCountGreaterOrEqualTo(2, "should have permissions for both users");
            permissions.UserPermissions.Should().Contain(p => p.UserId == user1Id, "should include first user");
            permissions.UserPermissions.Should().Contain(p => p.UserId == user2Id, "should include second user");
            
            foreach (var permission in permissions.UserPermissions)
            {
                permission.Id.Should().NotBeEmpty("permission should have a valid ID");
                permission.WalletId.Should().Be(createdWallet.WalletId, "permission should be for the correct wallet");
                permission.UserId.Should().NotBeNullOrEmpty("permission should have a user ID");
                permission.CreationDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(5), "creation date should be recent");
                permission.ExpirationDate.Should().BeAfter(DateTime.UtcNow, "expiration date should be in the future");
            }
        }

        [Fact(DisplayName = "Get User Permission For Specific User - Should return permissions for individual user")]
        public async Task GetUserPermissionForSpecificUser_ShouldReturnPermissions_ForIndividualUser()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and assign permissions to a specific user
             * 2. Retrieve permissions for that specific user
             * 3. Verify only that user's permissions are returned
             * 4. Verify permission details are complete
             * 
             * Expected Result:
             * - Only the specified user's permissions are returned
             * - Permission details match what was assigned
             * - Other users' permissions are not included
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            var userId = TestDataHelper.GenerateUserId();
            var permissionRequest = TestDataHelper.CreateUserPermissionRequest(userId, createdWallet.WalletId);
            
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/user-permissions", permissionRequest);

            // Act
            var getUserPermissionResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/user-permissions/{userId}");

            // Assert
            var userPermissions = await AssertSuccessAndDeserialize<List<UserPermission>>(getUserPermissionResponse);
            
            userPermissions.Should().NotBeEmpty("user should have permissions");
            userPermissions.Should().OnlyContain(p => p.UserId == userId, "should only return permissions for the specified user");
            
            var permission = userPermissions.First();
            permission.WalletId.Should().Be(createdWallet.WalletId, "permission should be for the correct wallet");
            permission.Admin.Should().BeTrue("user should have admin permissions");
            permission.Limits.Should().NotBeEmpty("user should have limits configured");
        }

        [Fact(DisplayName = "Revoke User Permissions - Should remove user access successfully")]
        public async Task RevokeUserPermissions_ShouldRemoveUserAccess_Successfully()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet and assign permissions to a user
             * 2. Verify the user has permissions
             * 3. Revoke the user's permissions
             * 4. Verify the permissions are removed/expired
             * 
             * Expected Result:
             * - Revocation operation succeeds
             * - User no longer has active permissions
             * - Revoked permissions are marked as expired or removed
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            var userId = TestDataHelper.GenerateUserId();
            var permissionRequest = TestDataHelper.CreateUserPermissionRequest(userId, createdWallet.WalletId);
            
            await Client.PostAsJsonAsync($"wallet/{createdWallet.WalletId}/user-permissions", permissionRequest);

            // Verify user has permissions before revocation
            var beforeRevokeResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/user-permissions/{userId}");
            var beforeRevokePermissions = await AssertSuccessAndDeserialize<List<UserPermission>>(beforeRevokeResponse);
            beforeRevokePermissions.Should().NotBeEmpty("user should have permissions before revocation");

            // Act
            var revokeResponse = await Client.PutAsync($"wallet/{createdWallet.WalletId}/user-permissions/{userId}/revoke", null);

            // Assert
            revokeResponse.StatusCode.Should().Be(HttpStatusCode.OK, "revocation should succeed");

            // Verify permissions are revoked (should return empty or expired permissions)
            var afterRevokeResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/user-permissions/{userId}");
            
            if (afterRevokeResponse.StatusCode == HttpStatusCode.OK)
            {
                var afterRevokePermissions = await DeserializeResponseAsync<List<UserPermission>>(afterRevokeResponse);
                if (afterRevokePermissions?.Any() == true)
                {
                    // If permissions still exist, they should be expired
                    afterRevokePermissions.Should().OnlyContain(p => p.ExpirationDate <= DateTime.UtcNow, 
                        "remaining permissions should be expired");
                }
            }
            else
            {
                // User should not be found or have no permissions
                afterRevokeResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.NoContent);
            }
        }

        [Fact(DisplayName = "Get Organization Users - Should return available users for wallet organization")]
        public async Task GetOrganizationUsers_ShouldReturnAvailableUsers_ForWalletOrganization()
        {
            // Arrange
            /* Test Logic:
             * 1. Create a wallet (which creates an organization context)
             * 2. Retrieve available users for the organization
             * 3. Verify users are returned with proper information
             * 4. Verify user data includes required fields
             * 
             * Expected Result:
             * - Available users are returned
             * - User information includes user ID and alias
             * - Users are associated with the wallet's organization
             */
            var walletName = TestDataHelper.GenerateUniqueWalletName();
            var registerRequest = TestDataHelper.CreateWalletRegistrationRequest(walletName);
            var registerResponse = await Client.PostAsJsonAsync("wallet/register", registerRequest);
            var createdWallet = await AssertSuccessAndDeserialize<WalletResponse>(registerResponse);

            // Act
            var organizationUsersResponse = await Client.GetAsync($"wallet/{createdWallet.WalletId}/users");

            // Assert
            var availableUsers = await AssertSuccessAndDeserialize<AvailableUserResponse>(organizationUsersResponse);
            
            availableUsers.Should().NotBeNull("available users response should not be null");
            availableUsers.Users.Should().NotBeNull("users list should not be null");
            
            if (availableUsers.Users.Any())
            {
                foreach (var user in availableUsers.Users)
                {
                    user.UserId.Should().NotBeNullOrEmpty("user should have a valid user ID");
                    user.Alias.Should().NotBeNullOrEmpty("user should have an alias");
                }
            }
        }
    }
}
